import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import dotenv from 'dotenv'
import { nodePolyfills } from 'vite-plugin-node-polyfills'

export default defineConfig(async ({ mode }) => {
  let proxy = undefined
  if (mode === 'development') {
    const serverEnv = dotenv.config({ processEnv: {}, path: '../server/.env' }).parsed
    const serverHost = serverEnv?.['HOST'] ?? 'localhost'
    const serverPort = parseInt(serverEnv?.['PORT'] ?? 3000)
    if (!Number.isNaN(serverPort) && serverPort > 0 && serverPort < 65535) {
      proxy = {
        '^/api(/|$).*': {
          target: `http://${serverHost}:${serverPort}`,
          changeOrigin: true
        },
        '^/s3-explorer(/|$).*': {
          target: `http://${serverHost}:${serverPort}`,
          changeOrigin: true
        },
        '/socket.io': {
          target: `http://${serverHost}:${serverPort}`,
          changeOrigin: true
        }
      }
    }
  }
  dotenv.config()
  return {
    base: '/',
    plugins: [
      react(),
      nodePolyfills({
        // To add only specific polyfills, add them here. If no option is passed, adds all polyfills
        include: ['process', 'util', 'buffer', 'stream', 'fs', 'path', 'zlib'],
        // To exclude specific polyfills, add them to this list. Note: if include is provided, this has no effect
        exclude: [],
        // Whether to polyfill specific globals.
        globals: {
          Buffer: true,
          global: true,
          process: true
        },
        // Whether to polyfill `node:` protocol imports.
        protocolImports: true
      })
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    root: resolve(__dirname),
    build: {
      outDir: './build'
    },
    server: {
      open: true,
      proxy,
      port: process.env.VITE_PORT ?? 8080,
      host: process.env.VITE_HOST
    },
    optimizeDeps: {
      include: ['mammoth', 'node-stream-zip']
    }
  }
})
