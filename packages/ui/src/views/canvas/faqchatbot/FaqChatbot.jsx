import { useState } from 'react'
import { useDispatch } from 'react-redux'
import PropTypes from 'prop-types'
import {
  <PERSON>ton,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  TextField,
  Box,
  Typography,
  Tooltip,
  CircularProgress
} from '@mui/material'
import { IconX, IconEdit, IconTrash, IconArrowsMaximize } from '@tabler/icons-react'
import useConfirm from '@/hooks/useConfirm'
import useNotifier from '@/utils/useNotifier'
import { enqueueSnackbar as enqueueSnackbarAction, closeSnackbar as closeSnackbarAction } from '@/store/actions'
import faqsApi from '@/api/faq'
import { useEffect } from 'react'
import * as XLSX from 'xlsx'
import HistoryDialog from './HistoryDialog'

export const FaqChatbot = ({ chatflowid, isAgentCanvas, chatflow }) => {
  const { confirm } = useConfirm()
  const dispatch = useDispatch()
  useNotifier()
  const enqueueSnackbar = (...args) => dispatch(enqueueSnackbarAction(...args))
  const closeSnackbar = (...args) => dispatch(closeSnackbarAction(...args))

  const [loadingGetData, setLoadingGetData] = useState(false)
  const [addingFaq, setAddingFaq] = useState(false)
  const [uploadingFile, setUploadingFile] = useState(false)

  const [open, setOpen] = useState(false)
  const [openUploadFile, setOpenUploadFile] = useState(false)

  const [faqs, setFaqs] = useState([])
  const [filterFaq, setFilterFaq] = useState([])
  const [editingIndex, setEditingIndex] = useState(null)
  const [question, setQuestion] = useState('')
  const [answer, setAnswer] = useState('')
  const [addFaqOpen, setAddFaqOpen] = useState(false)
  const [viewAnswerOpen, setViewAnswerOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [openPopupHistory, setOpenPopupHistory] = useState(false)

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen)
  }

  const handleAddFaq = async () => {
    try {
      if (!question) throw new Error('Vui lòng nhập câu hỏi')
      if (!answer) throw new Error('Vui lòng nhập câu trả lời')
      setAddingFaq(true)
      if (editingIndex !== null) {
        if (!faqs[editingIndex].id) {
          throw new Error('FAQ ID không tồn tại')
        }
        await faqsApi.updateFaq(faqs[editingIndex].id, { question, answer, chatflowId: chatflowid })

        const updatedFaqs = [...faqs]
        updatedFaqs[editingIndex] = { ...faqs[editingIndex], question, answer }
        setFaqs(updatedFaqs)
        setEditingIndex(null)
      } else {
        const document = await faqsApi.saveFaq({ question, answer, chatflowId: chatflowid })
        setFaqs([...faqs, document.data])
        if (faqs.length === 0) {
          handleUpdateSettings()
        }
      }
      enqueueSnackbar({
        message: editingIndex !== null ? 'Cập nhật câu hỏi thành công' : 'Thêm mới câu hỏi thành công',
        options: {
          key: new Date().getTime() + Math.random(),
          variant: 'success',
          persist: true,
          action: (key) => (
            <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
              <IconX />
            </Button>
          )
        }
      })
      setQuestion('')
      setAnswer('')
      setAddFaqOpen(false)
      setEditingIndex(null)
    } catch (error) {
      enqueueSnackbar({
        message: error?.message ? error?.message : editingIndex !== null ? 'Lỗi khi cập nhật FAQ' : 'Lỗi khi thêm FAQ',
        options: {
          key: new Date().getTime() + Math.random(),
          variant: 'error',
          persist: true,
          action: (key) => (
            <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
              <IconX />
            </Button>
          )
        }
      })
    } finally {
      setAddingFaq(false)
    }
  }

  const handleEditFaq = (index) => {
    setEditingIndex(index)
    setQuestion(faqs[index].question)
    setAnswer(faqs[index].answer)
    setAddFaqOpen(true)
  }

  const handleDeleteFaq = async (index) => {
    const confirmPayload = {
      title: `Xóa FAQ`,
      description: `Bạn có chắc chắn muốn xóa FAQ này không?`,
      confirmButtonName: 'Xóa',
      cancelButtonName: 'Hủy'
    }
    const isConfirmed = await confirm(confirmPayload)

    if (isConfirmed) {
      try {
        const res = await faqsApi.deleteFaq(faqs[index].id, chatflowid)
        if (res.status === 200) {
          enqueueSnackbar({
            message: 'Đã xóa FAQ',
            options: {
              key: new Date().getTime() + Math.random(),
              variant: 'success',
              action: (key) => (
                <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
                  <IconX />
                </Button>
              )
            }
          })
          const updatedFaqs = faqs.filter((_, i) => i !== index)
          setFaqs(updatedFaqs)
          setEditingIndex(null)
        }
      } catch (error) {
        enqueueSnackbar({
          message: 'Lỗi khi xóa FAQ',
          options: {
            key: new Date().getTime() + Math.random(),
            variant: 'error',
            persist: true,
            action: (key) => (
              <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
                <IconX />
              </Button>
            )
          }
        })
      }
    }
  }

  const handleViewAnswer = (index) => {
    setQuestion(faqs[index].question)
    setAnswer(faqs[index].answer)
    setViewAnswerOpen(true)
  }

  const handleOnChangeSearch = async (event) => {
    setSearchQuery(event.target.value)
    try {
      const filteredFaqs = faqs.filter((faq) => faq.question.toLowerCase().includes(event.target.value.toLowerCase()))

      setFilterFaq(filteredFaqs)
    } catch (error) {
      enqueueSnackbar({
        message: 'Lỗi khi tìm kiếm FAQs',
        options: {
          key: new Date().getTime() + Math.random(),
          variant: 'error',
          persist: true,
          action: (key) => (
            <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
              <IconX />
            </Button>
          )
        }
      })
    }
  }

  const handleOnchangeFile = async (event) => {
    const file = event.target.files[0]

    if (file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      enqueueSnackbar({
        message: 'Vui lòng chọn file định dạng .xlsx.',
        options: {
          key: new Date().getTime() + Math.random(),
          variant: 'error',
          persist: true,
          action: (key) => (
            <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
              <IconX />
            </Button>
          )
        }
      })
      return
    }
    setUploadingFile(true)
    const newFaqs = []

    const reader = new FileReader()
    reader.onload = async (e) => {
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      jsonData.shift()

      for (let i = 0; i < jsonData.length; i++) {
        const row = jsonData[i]
        if (row.length > 0 && typeof row[1] === 'string' && typeof row[2] === 'string' && row[1].trim() && row[2].trim()) {
          newFaqs.push({ question: row[1], answer: row[2] })
        } else {
          break
        }
      }
      console.log('🚀 ~ FaqChatbot.jsx:227 ~ jsonData.forEach ~ newFaqs:', newFaqs)

      try {
        const documents = await faqsApi.importFaqs({ faqsData: newFaqs, chatflowId: chatflowid })
        setFaqs((prevFaqs) => [...prevFaqs, ...documents.data])
        enqueueSnackbar({
          message: 'Nhập FAQ thành công',
          options: {
            key: new Date().getTime() + Math.random(),
            variant: 'success',
            action: (key) => (
              <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
                <IconX />
              </Button>
            )
          }
        })
        if (faqs.length === 0) {
          handleUpdateSettings()
        }
      } catch (error) {
        enqueueSnackbar({
          message: 'Lỗi khi nhập FAQ',
          options: {
            key: new Date().getTime() + Math.random(),
            variant: 'error',
            persist: true,
            action: (key) => (
              <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
                <IconX />
              </Button>
            )
          }
        })
      } finally {
        setUploadingFile(false)
      }
    }
    reader.readAsArrayBuffer(file)
  }

  const handleFetchFaqs = async () => {
    try {
      setLoadingGetData(true)
      const response = await faqsApi.getAllFaqs(chatflowid, 1000, 0)
      setFaqs(response.results)
    } catch (error) {
      console.log('🚀 ~ FaqChatbot.jsx:358 ~ handleFetchFaqs ~ error:', error)
    } finally {
      setLoadingGetData(false)
    }
  }

  useEffect(() => {
    if (chatflowid && open) {
      handleFetchFaqs()
    }
  }, [chatflowid, open])

  const handleClosePopupHistory = () => {
    setOpenPopupHistory(false)
  }

  const handleUpdateSettings = async () => {
    try {
      const response = await faqsApi.updateSettings(chatflowid)
      if (response.status === 200) {
        enqueueSnackbar({
          message: 'Cập nhật cài đặt thành công',
          options: {
            key: new Date().getTime() + Math.random(),
            variant: 'success',
            action: (key) => (
              <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
                <IconX />
              </Button>
            )
          }
        })
      }
    } catch (error) {
      enqueueSnackbar({
        message: 'Lỗi khi cập nhật cài đặt',
        options: {
          key: new Date().getTime() + Math.random(),
          variant: 'error',
          persist: true,
          action: (key) => (
            <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
              <IconX />
            </Button>
          )
        }
      })
    }
  }

  const handleDownloadTemplate = () => {
    const link = document.createElement('a')
    link.href = 'https://docs.google.com/spreadsheets/d/1MTNpuwp35yNDytcSu9TIu6_JQdBfzzOcsPSKeGxMiW0/export?format=xlsx'
    link.download = 'FAQs_Template.xlsx'
    link.click()
  }

  const addToFaqFromHistory = async (message) => {
    setAddFaqOpen(true)
    setQuestion(message.question)
    setAnswer(message.answer)
  }

  return (
    <>
      {open && (
        <HistoryDialog
          isFaq
          open={openPopupHistory}
          flowId={chatflowid}
          onClose={handleClosePopupHistory}
          addToFaqFromHistory={addToFaqFromHistory}
        />
      )}
      <IconButton
        size='small'
        sx={{
          height: 25,
          width: 25
        }}
        title='Expand'
        color='primary'
        onClick={handleToggle}
      >
        <IconArrowsMaximize />
      </IconButton>

      <Dialog
        open={open}
        fullWidth
        maxWidth='xl'
        onClose={() => {
          setOpen(false)
          setEditingIndex(null)
        }}
        aria-labelledby='alert-dialog-title'
        aria-describedby='alert-dialog-description'
        sx={{ overflow: 'visible' }}
      >
        <DialogTitle sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} id='alert-dialog-title'>
          <div className='flex items-center'>
            <Typography variant='h1'>FAQs</Typography>
            <input
              className='w-[288px]'
              style={{ marginLeft: '16px', padding: '8px', borderRadius: '12px', border: '1px solid #ccc', height: '40px' }}
              placeholder='Tìm kiếm trong FAQs'
              value={searchQuery}
              onChange={handleOnChangeSearch}
            />
          </div>

          <div className='flex items-center gap-3'>
            <label htmlFor='faq-file-input'>
              <Button variant='contained' onClick={() => setOpenUploadFile(true)} component='span' color='primary'>
                Import từ file excel
              </Button>
            </label>
            <Button variant='contained' onClick={() => setOpenPopupHistory(true)} color='info'>
              Thêm mới từ lịch sử
            </Button>
            <Button
              variant='contained'
              color='primary'
              onClick={() => {
                setAnswer('')
                setQuestion('')
                setAddFaqOpen(true)
              }}
            >
              Thêm mới
            </Button>
            <IconButton onClick={() => setOpen(false)}>
              <IconX />
            </IconButton>
          </div>
        </DialogTitle>

        <DialogContent className='cloud-dialog-wrapper' sx={{ display: 'flex', justifyContent: 'flex-end', flexDirection: 'column', p: 0 }}>
          <Box sx={{ p: 2, overflowY: 'auto' }}>
            {loadingGetData ? (
              <Typography variant='body1' sx={{ textAlign: 'center', mt: 2 }}>
                Đang tải dữ liệu...
              </Typography>
            ) : faqs?.length === 0 ? (
              <Typography variant='body1' sx={{ textAlign: 'center', mt: 2 }}>
                Không tìm thấy FAQs.
              </Typography>
            ) : (
              <List className='w-full'>
                {(searchQuery ? filterFaq : faqs)?.map((faq, index) => (
                  <ListItem key={index} className='mb-2 border border-gray-300 rounded-lg p-4 cursor-pointer hover:bg-gray-100'>
                    <Tooltip title='Nhấp để xem câu trả lời' placement='top'>
                      <ListItemText
                        primary={<Typography variant='subtitle1'>{faq.question}</Typography>}
                        onClick={() => handleViewAnswer(index)}
                      />
                    </Tooltip>
                    <ListItemSecondaryAction>
                      <IconButton edge='end' aria-label='edit' onClick={() => handleEditFaq(index)}>
                        <IconEdit />
                      </IconButton>
                      <IconButton edge='end' aria-label='delete' onClick={() => handleDeleteFaq(index)}>
                        <IconTrash />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            )}
          </Box>
        </DialogContent>
      </Dialog>

      <Dialog
        open={openUploadFile}
        fullWidth
        maxWidth='sm'
        onClose={() => setOpenUploadFile(false)}
        aria-labelledby='upload-file-dialog-title'
        aria-describedby='upload-file-dialog-description'
      >
        <DialogTitle
          sx={{ fontSize: '1.25rem', p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
          id='upload-file-dialog-title'
        >
          <h1>
            Upload File FAQs{' '}
            <span className='font-medium text-[16px]'>
              ( Tải về file mẫu:{' '}
              <button onClick={handleDownloadTemplate} className='text-blue-500'>
                FAQs_Template
              </button>{' '}
              )
            </span>
          </h1>
          <IconButton onClick={() => setOpenUploadFile(false)}>
            <IconX />
          </IconButton>
        </DialogTitle>
        <DialogContent
          sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', p: 2, height: '300px' }}
        >
          <Typography variant='body1' sx={{ mb: 2, textAlign: 'center' }}>
            Vui lòng chọn file định dạng .xlsx chứa các câu hỏi và câu trả lời. Mỗi câu hỏi và câu trả lời nên được đặt trên các dòng riêng
            biệt.
          </Typography>
          <label htmlFor='file-upload-input'>
            <Button variant='contained' component='span' color='primary' startIcon={<IconArrowsMaximize />} disabled={uploadingFile}>
              {uploadingFile && <CircularProgress size={24} sx={{ color: 'white' }} />}
              Chọn file
            </Button>
          </label>
          <input
            id='file-upload-input'
            type='file'
            accept='.xlsx'
            style={{ display: 'none' }}
            onChange={handleOnchangeFile}
            multiple={false}
          />
        </DialogContent>
      </Dialog>

      <Dialog
        open={addFaqOpen}
        fullWidth
        maxWidth='md'
        onClose={() => {
          setAddFaqOpen(false)
          setEditingIndex(null)
        }}
        aria-labelledby='add-faq-dialog-title'
        aria-describedby='add-faq-dialog-description'
      >
        <DialogTitle
          sx={{ fontSize: '1.25rem', p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
          id='add-faq-dialog-title'
        >
          {editingIndex !== null ? 'Chỉnh sửa FAQ' : 'Thêm FAQ'}
          <IconButton
            onClick={() => {
              setAddFaqOpen(false)
              setEditingIndex(null)
            }}
          >
            <IconX />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ display: 'flex', flexDirection: 'column', p: 2 }}>
          <span>Câu hỏi</span>
          <TextField fullWidth value={question} onChange={(e) => setQuestion(e.target.value)} sx={{ mb: 1, mt: 1 }} />
          <span>Câu trả lời</span>
          <textarea
            style={{
              width: '100%',
              padding: '16.5px 14px',
              borderRadius: '4px',
              border: '1px solid rgba(0, 0, 0, 0.23)',
              fontSize: '1rem',
              fontFamily: 'Roboto, Helvetica, Arial, sans-serif',
              lineHeight: '1.4375em',
              letterSpacing: '0.00938em',
              marginBottom: '16px'
            }}
            rows={12}
            value={answer}
            onChange={(e) => setAnswer(e.target.value)}
          />
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button variant='contained' color='secondary' onClick={() => setAddFaqOpen(false)}>
              Hủy bỏ
            </Button>
            <Button variant='contained' color='primary' onClick={handleAddFaq} disabled={addingFaq}>
              {addingFaq ? <CircularProgress size={24} sx={{ color: 'white' }} /> : editingIndex !== null ? 'Cập nhật FAQ' : 'Thêm FAQ'}
            </Button>
          </Box>
        </DialogContent>
      </Dialog>

      <Dialog
        open={viewAnswerOpen}
        fullWidth
        maxWidth='sm'
        onClose={() => setViewAnswerOpen(false)}
        aria-labelledby='view-answer-dialog-title'
        aria-describedby='view-answer-dialog-description'
      >
        <DialogTitle
          sx={{ fontSize: '1.25rem', p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
          id='view-answer-dialog-title'
        >
          Câu trả lời
          <IconButton onClick={() => setViewAnswerOpen(false)}>
            <IconX />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ display: 'flex', flexDirection: 'column', p: 2 }}>
          <Typography variant='subtitle1' sx={{ mb: 2 }}>
            <strong>Câu hỏi:</strong> {question}
          </Typography>
          <Typography variant='body1'>
            <strong>Câu trả lời:</strong> {answer}
          </Typography>
        </DialogContent>
      </Dialog>
    </>
  )
}

FaqChatbot.propTypes = { chatflowid: PropTypes.string, isAgentCanvas: PropTypes.bool, chatflow: PropTypes.any }
