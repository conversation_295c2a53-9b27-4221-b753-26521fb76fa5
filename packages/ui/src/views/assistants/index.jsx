import { useEffect, useState } from 'react'

// material-ui
import { Box, Stack, Button, Skeleton } from '@mui/material'

// project imports
import MainCard from '@/ui-component/cards/MainCard'
import ItemCard from '@/ui-component/cards/ItemCard'
import { gridSpacing } from '@/store/constant'
import AssistantEmptySVG from '@/assets/images/assistant_empty.svg'
import { StyledButton } from '@/ui-component/button/StyledButton'
import AssistantDialog from './AssistantDialog'
import LoadAssistantDialog from './LoadAssistantDialog'

// API
import assistantsApi from '@/api/assistants'

// Hooks
import useApi from '@/hooks/useApi'

// icons
import { IconPlus, IconFileUpload } from '@tabler/icons-react'
import ViewHeader from '@/layout/MainLayout/ViewHeader'
import ErrorBoundary from '@/ErrorBoundary'

// ==============================|| CHATFLOWS ||============================== //

const Assistants = () => {
  const getAllAssistantsApi = useApi(assistantsApi.getAllAssistants)

  const [isLoading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [showDialog, setShowDialog] = useState(false)
  const [dialogProps, setDialogProps] = useState({})
  const [showLoadDialog, setShowLoadDialog] = useState(false)
  const [loadDialogProps, setLoadDialogProps] = useState({})

  const loadExisting = () => {
    const dialogProp = {
      title: 'Load Existing Assistant'
    }
    setLoadDialogProps(dialogProp)
    setShowLoadDialog(true)
  }

  const [search, setSearch] = useState('')
  const onSearchChange = (event) => {
    setSearch(event.target.value)
  }

  const onAssistantSelected = (selectedOpenAIAssistantId, credential) => {
    setShowLoadDialog(false)
    addNew(selectedOpenAIAssistantId, credential)
  }

  const addNew = (selectedOpenAIAssistantId, credential) => {
    const dialogProp = {
      title: 'Thêm mới Assistant',
      type: 'ADD',
      cancelButtonName: 'Cancel',
      confirmButtonName: 'Add',
      selectedOpenAIAssistantId,
      credential
    }
    setDialogProps(dialogProp)
    setShowDialog(true)
  }

  const edit = (selectedAssistant) => {
    const dialogProp = {
      title: 'Edit Assistant',
      type: 'EDIT',
      cancelButtonName: 'Cancel',
      confirmButtonName: 'Save',
      data: selectedAssistant
    }
    setDialogProps(dialogProp)
    setShowDialog(true)
  }

  const onConfirm = () => {
    setShowDialog(false)
    getAllAssistantsApi.request()
  }

  function filterAssistants(data) {
    const parsedData = JSON.parse(data.details)
    return parsedData && parsedData.name && parsedData.name.toLowerCase().indexOf(search.toLowerCase()) > -1
  }

  useEffect(() => {
    getAllAssistantsApi.request()

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    setLoading(getAllAssistantsApi.loading)
  }, [getAllAssistantsApi.loading])

  useEffect(() => {
    if (getAllAssistantsApi.error) {
      setError(getAllAssistantsApi.error)
    }
  }, [getAllAssistantsApi.error])

  return (
    <>
      <MainCard>
        {error ? (
          <ErrorBoundary error={error} />
        ) : (
          <Stack flexDirection='column' sx={{ gap: 3 }}>
            <ViewHeader onSearchChange={onSearchChange} search={true} searchPlaceholder='Search Assistants' title='OpenAI Assistants'>
              <Button variant='outlined' onClick={loadExisting} startIcon={<IconFileUpload />} sx={{ borderRadius: 2, height: 40 }}>
                Load
              </Button>
              <StyledButton variant='contained' sx={{ borderRadius: 2, height: 40 }} onClick={addNew} startIcon={<IconPlus />}>
                Add
              </StyledButton>
            </ViewHeader>
            {isLoading ? (
              <Box display='grid' gridTemplateColumns='repeat(3, 1fr)' gap={gridSpacing}>
                <Skeleton variant='rounded' height={160} />
                <Skeleton variant='rounded' height={160} />
                <Skeleton variant='rounded' height={160} />
              </Box>
            ) : (
              <Box display='grid' gridTemplateColumns='repeat(3, 1fr)' gap={gridSpacing}>
                {getAllAssistantsApi.data &&
                  getAllAssistantsApi.data?.filter(filterAssistants).map((data, index) => (
                    <ItemCard
                      data={{
                        name: JSON.parse(data.details)?.name,
                        description: JSON.parse(data.details)?.instructions,
                        iconSrc: data.iconSrc
                      }}
                      key={index}
                      onClick={() => edit(data)}
                    />
                  ))}
              </Box>
            )}
            {!isLoading && (!getAllAssistantsApi.data || getAllAssistantsApi.data.length === 0) && (
              <Stack sx={{ alignItems: 'center', justifyContent: 'center' }} flexDirection='column'>
                <Box sx={{ p: 2, height: 'auto' }}>
                  <img style={{ objectFit: 'cover', height: '20vh', width: 'auto' }} src={AssistantEmptySVG} alt='AssistantEmptySVG' />
                </Box>
                <div>No Assistants Added Yet</div>
              </Stack>
            )}
          </Stack>
        )}
      </MainCard>
      <LoadAssistantDialog
        show={showLoadDialog}
        dialogProps={loadDialogProps}
        onCancel={() => setShowLoadDialog(false)}
        onAssistantSelected={onAssistantSelected}
        setError={setError}
      ></LoadAssistantDialog>
      <AssistantDialog
        show={showDialog}
        dialogProps={dialogProps}
        onCancel={() => setShowDialog(false)}
        onConfirm={onConfirm}
        setError={setError}
      ></AssistantDialog>
    </>
  )
}

export default Assistants
