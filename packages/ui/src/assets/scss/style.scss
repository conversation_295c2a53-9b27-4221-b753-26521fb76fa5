// Tailwind
//@tailwind base;
@tailwind components;
@tailwind utilities;

// color variants
@import 'themes-vars.module.scss';

// third-party
@import 'react-perfect-scrollbar/dist/css/styles.css';

// ==============================|| LIGHT BOX ||============================== //
.fullscreen .react-images__blanket {
  z-index: 1200;
}

// ==============================|| PERFECT SCROLLBAR ||============================== //

.scrollbar-container {
  .ps__rail-y {
    &:hover > .ps__thumb-y,
    &:focus > .ps__thumb-y,
    &.ps--clicking .ps__thumb-y {
      background-color: $grey500;
      width: 5px;
    }
  }

  .ps__thumb-y {
    background-color: $grey500;
    border-radius: 6px;
    width: 5px;
    right: 0;
  }
}

.scrollbar-container.ps,
.scrollbar-container > .ps {
  &.ps--active-y > .ps__rail-y {
    width: 5px;
    background-color: transparent !important;
    z-index: 999;

    &:hover,
    &.ps--clicking {
      width: 5px;
      background-color: transparent;
    }
  }

  &.ps--scrolling-y > .ps__rail-y,
  &.ps--scrolling-x > .ps__rail-x {
    opacity: 0.4;
    background-color: transparent;
  }
}

// ==============================|| ANIMATION KEYFRAMES ||============================== //

@keyframes wings {
  50% {
    transform: translateY(-40px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes blink {
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateZ(0);
  }
  40%,
  43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -5px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -7px, 0);
  }
  80% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateZ(0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes slideY {
  0%,
  50%,
  100% {
    transform: translateY(0px);
  }
  25% {
    transform: translateY(-10px);
  }
  75% {
    transform: translateY(10px);
  }
}

@keyframes slideX {
  0%,
  50%,
  100% {
    transform: translateX(0px);
  }
  25% {
    transform: translateX(-10px);
  }
  75% {
    transform: translateX(10px);
  }
}

[data-state='open'].z-50 {
  z-index: 1500 !important;
}

[role='dialog'] {
  z-index: 1600;
}

img,
video {
  height: fit-content;
}

.usermessage img {
  height: fit-content !important;
}

img[alt='agentPNG'] {
  height: 25px !important;
}

.MuiTooltip-popper.fixzindex {
  z-index: 999 !important;
}

// TODO: VIB only HIDE Sync Google button (not in PoC scope)
//#root
//  > div
//  > main
//  > div
//  > div
//  > div
//  > div.space-y-4.s3-explr-container
//  > div.flex.items-center.space-x-4.s3-explr-toolbar
//  > div.flex.items-center.gap-4 {
//  display: none;
//}

//#root
//  > div
//  > main
//  > div
//  > div
//  > div
//  > div.space-y-4.s3-explr-container
//  > div.flex.items-center.space-x-4.s3-explr-toolbar
//  > div.flex.items-center.gap-2.s3-explr-upload-group
//  > button.justify-center.whitespace-nowrap.font-medium.transition-colors.focus-visible\:outline-none.focus-visible\:ring-1.focus-visible\:ring-ring.disabled\:pointer-events-none.disabled\:opacity-50.\[\&_svg\]\:pointer-events-none.\[\&_svg\]\:size-4.\[\&_svg\]\:shrink-0.s3-explr-ui-button.border.border-input.bg-background.shadow-sm.hover\:bg-accent.hover\:text-accent-foreground.s3-explr-ui-button-outline.h-8.rounded-md.px-3.text-xs.s3-explr-ui-button-size-sm.flex.items-center.gap-2.s3-explr-add-website-btn {
//  display: none;
//}
