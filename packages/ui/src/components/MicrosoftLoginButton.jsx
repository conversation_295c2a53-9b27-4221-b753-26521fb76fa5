import React, { useState } from 'react'
import { Button, CircularProgress, Box } from '@mui/material'
import useMSAL from '@/hooks/useMSAL'

const MicrosoftLoginButton = ({ onError, disabled = false, fullWidth = true }) => {
  const [isLoading, setIsLoading] = useState(false)
  const { isInitialized, login, error: msalError } = useMSAL()

  const handleMicrosoftLogin = async () => {
    if (!isInitialized) {
      const errorMsg = 'Microsoft authentication is not ready. Please try again.'
      onError?.(errorMsg)
      return
    }

    if (isLoading) {
      return
    }

    setIsLoading(true)

    try {
      // Clear any existing interaction state
      localStorage.removeItem('microsoftOAuthInProgress')

      // Set flag to indicate Microsoft OAuth is in progress
      localStorage.setItem('microsoftOAuthInProgress', 'true')

      await login(['User.Read'])
      // The redirect will happen, so we don't need to do anything else here
    } catch (error) {
      // Clear flag if there's an error
      localStorage.removeItem('microsoftOAuthInProgress')

      const errorMsg = error.message || 'Microsoft login failed. Please try again.'
      onError?.(errorMsg)
    } finally {
      setIsLoading(false)
    }
  }

  // Show error state if MSAL failed to initialize
  if (msalError) {
    return (
      <Button
        fullWidth={fullWidth}
        variant='outlined'
        disabled
        sx={{
          mt: 1,
          py: 1.5,
          borderColor: '#f44336',
          color: '#f44336',
          '&:hover': {
            borderColor: '#f44336',
            backgroundColor: 'rgba(244, 67, 54, 0.04)'
          }
        }}
      >
        Microsoft Login Unavailable
      </Button>
    )
  }

  return (
    <Button
      fullWidth={fullWidth}
      variant='outlined'
      onClick={handleMicrosoftLogin}
      disabled={disabled || !isInitialized || isLoading}
      sx={{
        mt: 1,
        py: 1.5,
        borderColor: '#0078d4',
        color: '#0078d4',
        backgroundColor: 'white',
        '&:hover': {
          borderColor: '#106ebe',
          backgroundColor: 'rgba(0, 120, 212, 0.04)',
          color: '#106ebe'
        },
        '&:disabled': {
          borderColor: '#e0e0e0',
          color: '#9e9e9e'
        }
      }}
      startIcon={
        isLoading ? (
          <CircularProgress size={20} sx={{ color: '#0078d4' }} />
        ) : (
          <Box
            component='img'
            src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMSIgeT0iMSIgd2lkdGg9IjkiIGhlaWdodD0iOSIgZmlsbD0iI0Y0NTIzNiIvPgo8cmVjdCB4PSIxMSIgeT0iMSIgd2lkdGg9IjkiIGhlaWdodD0iOSIgZmlsbD0iIzAwQkNGMiIvPgo8cmVjdCB4PSIxIiB5PSIxMSIgd2lkdGg9IjkiIGhlaWdodD0iOSIgZmlsbD0iI0ZGQjkwMCIvPgo8cmVjdCB4PSIxMSIgeT0iMTEiIHdpZHRoPSI5IiBoZWlnaHQ9IjkiIGZpbGw9IiMwMEE5NTIiLz4KPC9zdmc+'
            alt='Microsoft'
            sx={{ width: 20, height: 20 }}
          />
        )
      }
    >
      {isLoading ? 'Đang xử lý...' : 'Đăng nhập với Microsoft'}
    </Button>
  )
}

export default MicrosoftLoginButton
