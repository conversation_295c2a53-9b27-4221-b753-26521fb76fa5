{"description": "Return response as a specified JSON structure instead of a string/text", "framework": ["Langchain"], "usecases": ["Extraction"], "nodes": [{"width": 300, "height": 574, "id": "chatOpenAI_0", "position": {"x": 845.3961479115309, "y": -205.74401580699953}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6.0, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo", "temperature": "0", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 845.3961479115309, "y": -205.74401580699953}, "dragging": false}, {"width": 300, "height": 456, "id": "llmChain_0", "position": {"x": 1229.1699649849293, "y": 245.55173505632646}, "type": "customNode", "data": {"id": "llmChain_0", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_0-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_0-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_0-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_0-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_0-input-inputModeration-Moderation"}], "inputs": {"model": "{{chatOpenAI_0.data.instance}}", "prompt": "{{chatPromptTemplate_0.data.instance}}", "outputParser": "{{structuredOutputParser_0.data.instance}}", "chainName": "", "inputModeration": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_0-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_0-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": false}, "positionAbsolute": {"x": 1229.1699649849293, "y": 245.55173505632646}, "selected": false}, {"width": 300, "height": 652, "id": "chatPromptTemplate_0", "position": {"x": 501.1597501123828, "y": -154.43917602832562}, "type": "customNode", "data": {"id": "chatPromptTemplate_0", "label": "Chat Prompt Template", "version": 1, "name": "chatPromptTemplate", "type": "ChatPromptTemplate", "baseClasses": ["ChatPromptTemplate", "BaseChatPromptTemplate", "BasePromptTemplate", "Runnable"], "category": "Prompts", "description": "Schema to represent a chat prompt", "inputParams": [{"label": "System Message", "name": "systemMessagePrompt", "type": "string", "rows": 4, "placeholder": "You are a helpful assistant that translates {input_language} to {output_language}.", "id": "chatPromptTemplate_0-input-systemMessagePrompt-string"}, {"label": "Human Message", "name": "humanMessagePrompt", "type": "string", "rows": 4, "placeholder": "{text}", "id": "chatPromptTemplate_0-input-humanMessagePrompt-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "chatPromptTemplate_0-input-promptValues-json"}], "inputAnchors": [], "inputs": {"systemMessagePrompt": "Answer user's question as best you can", "humanMessagePrompt": "{text}", "promptValues": ""}, "outputAnchors": [{"id": "chatPromptTemplate_0-output-chatPromptTemplate-ChatPromptTemplate|BaseChatPromptTemplate|BasePromptTemplate|Runnable", "name": "chatPromptTemplate", "label": "ChatPromptTemplate", "type": "ChatPromptTemplate | BaseChatPromptTemplate | BasePromptTemplate | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 501.1597501123828, "y": -154.43917602832562}, "dragging": false}, {"width": 300, "height": 328, "id": "structuredOutputParser_0", "position": {"x": 170.3869571939727, "y": 343.9298288967859}, "type": "customNode", "data": {"id": "structuredOutputParser_0", "label": "Structured Output Parser", "version": 1, "name": "structuredOutputParser", "type": "StructuredOutputParser", "baseClasses": ["StructuredOutputParser", "BaseLLMOutputParser", "Runnable"], "category": "Output Parsers", "description": "Parse the output of an LLM call into a given (JSON) structure.", "inputParams": [{"label": "Autofix", "name": "autofixParser", "type": "boolean", "optional": true, "description": "In the event that the first call fails, will make another call to the model to fix any errors.", "id": "structuredOutputParser_0-input-autofixParser-boolean"}, {"label": "JSON Structure", "name": "jsonStructure", "type": "datagrid", "description": "JSON structure for LLM to return", "datagrid": [{"field": "property", "headerName": "Property", "editable": true}, {"field": "type", "headerName": "Type", "type": "singleSelect", "valueOptions": ["string", "number", "boolean"], "editable": true}, {"field": "description", "headerName": "Description", "editable": true, "flex": 1}], "default": [{"property": "answer", "type": "string", "description": "answer to the user's question"}, {"property": "source", "type": "string", "description": "sources used to answer the question, should be websites"}], "additionalParams": true, "id": "structuredOutputParser_0-input-jsonStructure-datagrid"}], "inputAnchors": [], "inputs": {"autofixParser": true, "jsonStructure": [{"property": "answer", "type": "string", "description": "answer to the user's question"}, {"property": "source", "type": "string", "description": "sources used to answer the question, should be websites"}]}, "outputAnchors": [{"id": "structuredOutputParser_0-output-structuredOutputParser-StructuredOutputParser|BaseLLMOutputParser|Runnable", "name": "structuredOutputParser", "label": "StructuredOutputParser", "type": "StructuredOutputParser | BaseLLMOutputParser | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 170.3869571939727, "y": 343.9298288967859}, "dragging": false}], "edges": [{"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "llmChain_0", "targetHandle": "llmChain_0-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-llmChain_0-llmChain_0-input-model-BaseLanguageModel", "data": {"label": ""}}, {"source": "chatPromptTemplate_0", "sourceHandle": "chatPromptTemplate_0-output-chatPromptTemplate-ChatPromptTemplate|BaseChatPromptTemplate|BasePromptTemplate|Runnable", "target": "llmChain_0", "targetHandle": "llmChain_0-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "chatPromptTemplate_0-chatPromptTemplate_0-output-chatPromptTemplate-ChatPromptTemplate|BaseChatPromptTemplate|BasePromptTemplate|Runnable-llmChain_0-llmChain_0-input-prompt-BasePromptTemplate", "data": {"label": ""}}, {"source": "structuredOutputParser_0", "sourceHandle": "structuredOutputParser_0-output-structuredOutputParser-StructuredOutputParser|BaseLLMOutputParser|Runnable", "target": "llmChain_0", "targetHandle": "llmChain_0-input-outputParser-BaseLLMOutputParser", "type": "buttonedge", "id": "structuredOutputParser_0-structuredOutputParser_0-output-structuredOutputParser-StructuredOutputParser|BaseLLMOutputParser|Runnable-llmChain_0-llmChain_0-input-outputParser-BaseLLMOutputParser", "data": {"label": ""}}]}