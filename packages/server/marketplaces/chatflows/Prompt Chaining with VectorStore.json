{"description": "Use chat history to rephrase user question, then answer the rephrased question using retrieved docs from vector store", "usecases": ["Documents QnA"], "framework": ["Langchain"], "nodes": [{"width": 300, "height": 511, "id": "promptTemplate_0", "position": {"x": 344.73370692733414, "y": -122.34815000085804}, "type": "customNode", "data": {"id": "promptTemplate_0", "label": "Prompt Template", "version": 1, "name": "promptTemplate", "type": "PromptTemplate", "baseClasses": ["PromptTemplate", "BaseStringPromptTemplate", "BasePromptTemplate", "Runnable"], "category": "Prompts", "description": "Schema to represent a basic prompt for an LLM", "inputParams": [{"label": "Template", "name": "template", "type": "string", "rows": 4, "placeholder": "What is a good name for a company that makes {product}?", "id": "promptTemplate_0-input-template-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "promptTemplate_0-input-promptValues-json"}], "inputAnchors": [], "inputs": {"template": "Given the following conversation and a follow up question, rephrase the follow up question to be a standalone question.\n\nChat History:\n{chat_history}\nFollow Up Input: {question}\nStandalone question:", "promptValues": "{\"question\":\"{{question}}\",\"chat_history\":\"{{chat_history}}\"}"}, "outputAnchors": [{"id": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "name": "promptTemplate", "label": "PromptTemplate", "type": "PromptTemplate | BaseStringPromptTemplate | BasePromptTemplate | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 344.73370692733414, "y": -122.34815000085804}, "dragging": false}, {"width": 300, "height": 688, "id": "chatPromptTemplate_0", "position": {"x": 2314.*************, "y": -163.**************}, "type": "customNode", "data": {"id": "chatPromptTemplate_0", "label": "Chat Prompt Template", "version": 1, "name": "chatPromptTemplate", "type": "ChatPromptTemplate", "baseClasses": ["ChatPromptTemplate", "BaseChatPromptTemplate", "BasePromptTemplate", "Runnable"], "category": "Prompts", "description": "Schema to represent a chat prompt", "inputParams": [{"label": "System Message", "name": "systemMessagePrompt", "type": "string", "rows": 4, "placeholder": "You are a helpful assistant that translates {input_language} to {output_language}.", "id": "chatPromptTemplate_0-input-systemMessagePrompt-string"}, {"label": "Human Message", "name": "humanMessagePrompt", "type": "string", "rows": 4, "placeholder": "{text}", "id": "chatPromptTemplate_0-input-humanMessagePrompt-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "chatPromptTemplate_0-input-promptValues-json"}], "inputAnchors": [], "inputs": {"systemMessagePrompt": "Using the provided context, answer the user's question to the best of your ability using the resources provided. If there is nothing in the context relevant to the question at hand, just say \"Hmm, I'm not sure.\" Don't try to make up an answer.\n\nAnything between the following \\`context\\`  html blocks is retrieved from a knowledge bank, not part of the conversation with the user.\n\n<context>\n    {context}\n<context/>\n\nREMEMBER: If there is no relevant information within the context, just say \"Hmm, I'm not sure.\" Don't try to make up an answer. Anything between the preceding 'context' html blocks is retrieved from a knowledge bank, not part of the conversation with the user.", "humanMessagePrompt": "{text}", "promptValues": "{\"context\":\"{{vectorStoreToDocument_0.data.instance}}\",\"text\":\"{{question}}\"}"}, "outputAnchors": [{"id": "chatPromptTemplate_0-output-chatPromptTemplate-ChatPromptTemplate|BaseChatPromptTemplate|BasePromptTemplate|Runnable", "name": "chatPromptTemplate", "label": "ChatPromptTemplate", "type": "ChatPromptTemplate | BaseChatPromptTemplate | BasePromptTemplate | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 2314.*************, "y": -163.**************}, "dragging": false}, {"width": 300, "height": 454, "id": "vectorStoreToDocument_0", "position": {"x": 1906.*************, "y": -157.*************}, "type": "customNode", "data": {"id": "vectorStoreToDocument_0", "label": "VectorStore To Document", "version": 2, "name": "vectorStoreToDocument", "type": "Document", "baseClasses": ["Document"], "category": "Document Loaders", "description": "Search documents with scores from vector store", "inputParams": [{"label": "Query", "name": "query", "type": "string", "description": "Query to retrieve documents from vector database. If not specified, user question will be used", "optional": true, "acceptVariable": true, "id": "vectorStoreToDocument_0-input-query-string"}, {"label": "Minimum Score (%)", "name": "minScore", "type": "number", "optional": true, "placeholder": "75", "step": 1, "description": "Minumum score for embeddings documents to be included", "id": "vectorStoreToDocument_0-input-minScore-number"}], "inputAnchors": [{"label": "Vector Store", "name": "vectorStore", "type": "VectorStore", "id": "vectorStoreToDocument_0-input-vectorStore-VectorStore"}], "inputs": {"vectorStore": "{{singlestore_0.data.instance}}", "query": "{{llmChain_2.data.instance}}", "minScore": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "vectorStoreToDocument_0-output-document-Document|json", "name": "document", "label": "Document", "type": "Document | json"}, {"id": "vectorStoreToDocument_0-output-text-string|json", "name": "text", "label": "Text", "type": "string | json"}], "default": "document"}], "outputs": {"output": "text"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1906.*************, "y": -157.*************}, "dragging": false}, {"width": 300, "height": 507, "id": "llmChain_2", "position": {"x": 756.2678342825631, "y": -244.07972550448233}, "type": "customNode", "data": {"id": "llmChain_2", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_2-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_2-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_2-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_2-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_2-input-inputModeration-Moderation"}], "inputs": {"model": "{{chatOpenAI_0.data.instance}}", "prompt": "{{promptTemplate_0.data.instance}}", "outputParser": "", "chainName": "RephraseQuestion", "inputModeration": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_2-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_2-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "outputPrediction"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 756.2678342825631, "y": -244.07972550448233}, "dragging": false}, {"width": 300, "height": 507, "id": "llmChain_1", "position": {"x": 2716.1571046184436, "y": -279.02657697343375}, "type": "customNode", "data": {"id": "llmChain_1", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_1-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_1-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_1-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_1-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_1-input-inputModeration-Moderation"}], "inputs": {"model": "{{chatOpenAI_1.data.instance}}", "prompt": "{{chatPromptTemplate_0.data.instance}}", "outputParser": "", "chainName": "FinalResponse", "inputModeration": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_1-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_1-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 2716.1571046184436, "y": -279.02657697343375}, "dragging": false}, {"width": 300, "height": 669, "id": "chatOpenAI_0", "position": {"x": 344.77878441903204, "y": -832.2188929689953}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo-16k", "temperature": "0", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 344.77878441903204, "y": -832.2188929689953}, "dragging": false}, {"width": 300, "height": 669, "id": "chatOpenAI_1", "position": {"x": 2296.3207911691625, "y": -880.514745028577}, "type": "customNode", "data": {"id": "chatOpenAI_1", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_1-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_1-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_1-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_1-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_1-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_1-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_1-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo-16k", "temperature": "0", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 2296.3207911691625, "y": -880.514745028577}, "dragging": false}, {"width": 300, "height": 652, "id": "singlestore_0", "position": {"x": 1530.532503048084, "y": -657.3586990397077}, "type": "customNode", "data": {"id": "singlestore_0", "label": "SingleStore", "version": 1, "name": "singlestore", "type": "SingleStore", "baseClasses": ["SingleStore", "VectorStoreRetriever", "BaseRetriever"], "category": "Vector Stores", "description": "Upsert embedded data and perform similarity search upon query using SingleStore, a fast and distributed cloud relational database", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "description": "Needed when using SingleStore cloud hosted", "optional": true, "credentialNames": ["singleStoreApi"], "id": "singlestore_0-input-credential-credential"}, {"label": "Host", "name": "host", "type": "string", "id": "singlestore_0-input-host-string"}, {"label": "Database", "name": "database", "type": "string", "id": "singlestore_0-input-database-string"}, {"label": "Table Name", "name": "tableName", "type": "string", "placeholder": "embeddings", "additionalParams": true, "optional": true, "id": "singlestore_0-input-tableName-string"}, {"label": "Content Column Name", "name": "contentColumnName", "type": "string", "placeholder": "content", "additionalParams": true, "optional": true, "id": "singlestore_0-input-contentColumnName-string"}, {"label": "Vector Column Name", "name": "vectorColumnName", "type": "string", "placeholder": "vector", "additionalParams": true, "optional": true, "id": "singlestore_0-input-vectorColumnName-string"}, {"label": "Metadata Column Name", "name": "metadataColumnName", "type": "string", "placeholder": "metadata", "additionalParams": true, "optional": true, "id": "singlestore_0-input-metadataColumnName-string"}, {"label": "Top K", "name": "topK", "placeholder": "4", "type": "number", "additionalParams": true, "optional": true, "id": "singlestore_0-input-topK-number"}], "inputAnchors": [{"label": "Document", "name": "document", "type": "Document", "list": true, "optional": true, "id": "singlestore_0-input-document-Document"}, {"label": "Embeddings", "name": "embeddings", "type": "Embeddings", "id": "singlestore_0-input-embeddings-Embeddings"}], "inputs": {"document": "", "embeddings": "{{openAIEmbeddings_0.data.instance}}", "host": "", "database": "", "tableName": "", "contentColumnName": "", "vectorColumnName": "", "metadataColumnName": "", "topK": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "singlestore_0-output-retriever-SingleStore|VectorStoreRetriever|BaseRetriever", "name": "retriever", "label": "SingleStore Retriever", "type": "SingleStore | VectorStoreRetriever | BaseRetriever"}, {"id": "singlestore_0-output-vectorStore-SingleStore|VectorStore", "name": "vectorStore", "label": "SingleStore Vector Store", "type": "SingleStore | VectorStore"}], "default": "retriever"}], "outputs": {"output": "vectorStore"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1530.532503048084, "y": -657.3586990397077}, "dragging": false}, {"width": 300, "height": 423, "id": "openAIEmbeddings_0", "position": {"x": 1154.293946350955, "y": -589.6072684085893}, "type": "customNode", "data": {"id": "openAIEmbeddings_0", "label": "OpenAI Embeddings", "version": 4, "name": "openAIEmbeddings", "type": "OpenAIEmbeddings", "baseClasses": ["OpenAIEmbeddings", "Embeddings"], "category": "Embeddings", "description": "OpenAI API to generate embeddings for a given text", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "openAIEmbeddings_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "text-embedding-ada-002", "id": "openAIEmbeddings_0-input-modelName-asyncOptions"}, {"label": "Strip New Lines", "name": "stripNewLines", "type": "boolean", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-stripNewLines-boolean"}, {"label": "<PERSON><PERSON> Si<PERSON>", "name": "batchSize", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-batchSize-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-basepath-string"}, {"label": "Dimensions", "name": "dimensions", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-dimensions-number"}], "inputAnchors": [], "inputs": {"modelName": "text-embedding-ada-002", "stripNewLines": "", "batchSize": "", "timeout": "", "basepath": "", "dimensions": ""}, "outputAnchors": [{"id": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "name": "openAIEmbeddings", "label": "OpenAIEmbeddings", "description": "OpenAI API to generate embeddings for a given text", "type": "OpenAIEmbeddings | Embeddings"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1154.293946350955, "y": -589.6072684085893}, "dragging": false}, {"id": "stickyNote_0", "position": {"x": 753.8985547694751, "y": -597.2403700691232}, "type": "stickyNote", "data": {"id": "stickyNote_0", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_0-input-note-string"}], "inputAnchors": [], "inputs": {"note": "First, we rephrase the question using context from previous conversation.\n\nThis is to ensure that a follow-up question can be asked. For example:\n\n- What is the address of the Bakery shop?\n- What about the opening time?\n\nA rephrased question will be:\n- What is the opening time of the Bakery shop?\n\nThis ensure a better search to vector store, hence better results"}, "outputAnchors": [{"id": "stickyNote_0-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 324, "selected": false, "positionAbsolute": {"x": 753.8985547694751, "y": -597.2403700691232}, "dragging": false}, {"id": "stickyNote_1", "position": {"x": 1904.305205441637, "y": -241.45986503369568}, "type": "stickyNote", "data": {"id": "stickyNote_1", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_1-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Second, rephrased question is used to do a similarity search to find relevant context"}, "outputAnchors": [{"id": "stickyNote_1-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 62, "selected": false, "positionAbsolute": {"x": 1904.305205441637, "y": -241.45986503369568}, "dragging": false}, {"id": "stickyNote_2", "position": {"x": 2717.983596010546, "y": -369.73223420234956}, "type": "stickyNote", "data": {"id": "stickyNote_2", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_2-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Last, using the context from vector store, we instruct LLM to give a final response"}, "outputAnchors": [{"id": "stickyNote_2-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 62, "selected": false, "positionAbsolute": {"x": 2717.983596010546, "y": -369.73223420234956}, "dragging": false}], "edges": [{"source": "vectorStoreToDocument_0", "sourceHandle": "vectorStoreToDocument_0-output-text-string|json", "target": "chatPromptTemplate_0", "targetHandle": "chatPromptTemplate_0-input-promptValues-json", "type": "buttonedge", "id": "vectorStoreToDocument_0-vectorStoreToDocument_0-output-text-string|json-chatPromptTemplate_0-chatPromptTemplate_0-input-promptValues-json", "data": {"label": ""}}, {"source": "promptTemplate_0", "sourceHandle": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "target": "llmChain_2", "targetHandle": "llmChain_2-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "promptTemplate_0-promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable-llmChain_2-llmChain_2-input-prompt-BasePromptTemplate", "data": {"label": ""}}, {"source": "llmChain_2", "sourceHandle": "llmChain_2-output-outputPrediction-string|json", "target": "vectorStoreToDocument_0", "targetHandle": "vectorStoreToDocument_0-input-query-string", "type": "buttonedge", "id": "llmChain_2-llmChain_2-output-outputPrediction-string|json-vectorStoreToDocument_0-vectorStoreToDocument_0-input-query-string", "data": {"label": ""}}, {"source": "chatPromptTemplate_0", "sourceHandle": "chatPromptTemplate_0-output-chatPromptTemplate-ChatPromptTemplate|BaseChatPromptTemplate|BasePromptTemplate|Runnable", "target": "llmChain_1", "targetHandle": "llmChain_1-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "chatPromptTemplate_0-chatPromptTemplate_0-output-chatPromptTemplate-ChatPromptTemplate|BaseChatPromptTemplate|BasePromptTemplate|Runnable-llmChain_1-llmChain_1-input-prompt-BasePromptTemplate", "data": {"label": ""}}, {"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "llmChain_2", "targetHandle": "llmChain_2-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-llmChain_2-llmChain_2-input-model-BaseLanguageModel", "data": {"label": ""}}, {"source": "chatOpenAI_1", "sourceHandle": "chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "llmChain_1", "targetHandle": "llmChain_1-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_1-chatOpenAI_1-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-llmChain_1-llmChain_1-input-model-BaseLanguageModel", "data": {"label": ""}}, {"source": "singlestore_0", "sourceHandle": "singlestore_0-output-vectorStore-SingleStore|VectorStore", "target": "vectorStoreToDocument_0", "targetHandle": "vectorStoreToDocument_0-input-vectorStore-VectorStore", "type": "buttonedge", "id": "singlestore_0-singlestore_0-output-vectorStore-SingleStore|VectorStore-vectorStoreToDocument_0-vectorStoreToDocument_0-input-vectorStore-VectorStore", "data": {"label": ""}}, {"source": "openAIEmbeddings_0", "sourceHandle": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "target": "singlestore_0", "targetHandle": "singlestore_0-input-embeddings-Embeddings", "type": "buttonedge", "id": "openAIEmbeddings_0-openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings-singlestore_0-singlestore_0-input-embeddings-Embeddings", "data": {"label": ""}}]}