{"description": "Return response as a list (array) instead of a string/text", "usecases": ["Extraction"], "framework": ["Langchain"], "nodes": [{"width": 300, "height": 508, "id": "llmChain_0", "position": {"x": 1490.4252662385359, "y": 229.91198307750102}, "type": "customNode", "data": {"id": "llmChain_0", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_0-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_0-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_0-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_0-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_0-input-inputModeration-Moderation"}], "inputs": {"model": "{{chatOpenAI_0.data.instance}}", "prompt": "{{promptTemplate_0.data.instance}}", "outputParser": "{{csvOutputParser_0.data.instance}}", "chainName": "", "inputModeration": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_0-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_0-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1490.4252662385359, "y": 229.91198307750102}, "dragging": false}, {"width": 300, "height": 277, "id": "csvOutputParser_0", "position": {"x": 475.6669697284608, "y": 372.431864986419}, "type": "customNode", "data": {"id": "csvOutputParser_0", "label": "CSV Output Parser", "version": 1, "name": "csvOutputParser", "type": "CSVListOutputParser", "baseClasses": ["CSVListOutputParser", "BaseLLMOutputParser", "Runnable"], "category": "Output Parsers", "description": "Parse the output of an LLM call as a comma-separated list of values", "inputParams": [{"label": "Autofix", "name": "autofixParser", "type": "boolean", "optional": true, "description": "In the event that the first call fails, will make another call to the model to fix any errors.", "id": "csvOutputParser_0-input-autofixParser-boolean"}], "inputAnchors": [], "inputs": {"autofixParser": true}, "outputAnchors": [{"id": "csvOutputParser_0-output-csvOutputParser-CSVListOutputParser|BaseLLMOutputParser|Runnable", "name": "csvOutputParser", "label": "CSVListOutputParser", "type": "CSVListOutputParser | BaseLLMOutputParser | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 475.6669697284608, "y": 372.431864986419}, "dragging": false}, {"width": 300, "height": 513, "id": "promptTemplate_0", "position": {"x": 804.3731431892371, "y": -27.66112032134788}, "type": "customNode", "data": {"id": "promptTemplate_0", "label": "Prompt Template", "version": 1, "name": "promptTemplate", "type": "PromptTemplate", "baseClasses": ["PromptTemplate", "BaseStringPromptTemplate", "BasePromptTemplate", "Runnable"], "category": "Prompts", "description": "Schema to represent a basic prompt for an LLM", "inputParams": [{"label": "Template", "name": "template", "type": "string", "rows": 4, "placeholder": "What is a good name for a company that makes {product}?", "id": "promptTemplate_0-input-template-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "promptTemplate_0-input-promptValues-json"}], "inputAnchors": [], "inputs": {"template": "Answer user's question as best you can: {question}", "promptValues": ""}, "outputAnchors": [{"id": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "name": "promptTemplate", "label": "PromptTemplate", "type": "PromptTemplate | BaseStringPromptTemplate | BasePromptTemplate | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 804.3731431892371, "y": -27.66112032134788}, "dragging": false}, {"width": 300, "height": 670, "id": "chatOpenAI_0", "position": {"x": 1140.3848027357826, "y": -293.0678333630858}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo-16k", "temperature": "0", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1140.3848027357826, "y": -293.0678333630858}, "dragging": false}, {"id": "stickyNote_0", "position": {"x": 470.58135005141685, "y": 265.982559487312}, "type": "stickyNote", "data": {"id": "stickyNote_0", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_0-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Turning on Autofix allows LLM to automatically correct itself if output is not an array"}, "outputAnchors": [{"id": "stickyNote_0-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 82, "selected": false, "positionAbsolute": {"x": 470.58135005141685, "y": 265.982559487312}, "dragging": false}, {"id": "stickyNote_1", "position": {"x": 1482.7892542600414, "y": 120.12427436791523}, "type": "stickyNote", "data": {"id": "stickyNote_1", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_1-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Example question:\n\n- top 10 movies"}, "outputAnchors": [{"id": "stickyNote_1-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 82, "selected": false, "positionAbsolute": {"x": 1482.7892542600414, "y": 120.12427436791523}, "dragging": false}], "edges": [{"source": "csvOutputParser_0", "sourceHandle": "csvOutputParser_0-output-csvOutputParser-CSVListOutputParser|BaseLLMOutputParser|Runnable", "target": "llmChain_0", "targetHandle": "llmChain_0-input-outputParser-BaseLLMOutputParser", "type": "buttonedge", "id": "csvOutputParser_0-csvOutputParser_0-output-csvOutputParser-CSVListOutputParser|BaseLLMOutputParser|Runnable-llmChain_0-llmChain_0-input-outputParser-BaseLLMOutputParser", "data": {"label": ""}}, {"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "llmChain_0", "targetHandle": "llmChain_0-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-llmChain_0-llmChain_0-input-model-BaseLanguageModel", "data": {"label": ""}}, {"source": "promptTemplate_0", "sourceHandle": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "target": "llmChain_0", "targetHandle": "llmChain_0-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "promptTemplate_0-promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable-llmChain_0-llmChain_0-input-prompt-BasePromptTemplate", "data": {"label": ""}}]}