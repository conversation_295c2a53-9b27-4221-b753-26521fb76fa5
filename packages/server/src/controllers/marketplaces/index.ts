import { NextFunction, Request, Response } from 'express'
import marketplacesService from '../../services/marketplaces'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { StatusCodes } from 'http-status-codes'

// Get all templates for marketplaces
const getAllTemplates = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const keepTemplateNames = new Set([
      'AutoGPT',
      'WebBrowser',
      'Translator',
      'Text to SQL',
      'Multi Agents',
      'ReAct Agent',
      'Local QnA',
      'Send Teams Message',
      'IfElse',
      'WebPage QnA',
      'Flowise Docs QnA',
      'Conversation Chain',
      'Conversational Retrieval QA Chain',
      'Multiple Documents QnA',
      'WebPage QnA'
    ])

    const apiResponse = await marketplacesService.getAllTemplates()
    return res.json(
      apiResponse
        .filter((template) => keepTemplateNames.has(template.templateName))
        .map((template: any) => {
          const templateStr = JSON.stringify(template).replace(new RegExp('Flowise', 'gi'), 'C-Agent')

          return JSON.parse(templateStr)
        })
    )
  } catch (error) {
    next(error)
  }
}

const deleteCustomTemplate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (typeof req.params === 'undefined' || !req.params.id) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, `Error: marketplacesService.deleteCustomTemplate - id not provided!`)
    }
    const apiResponse = await marketplacesService.deleteCustomTemplate(req.params.id)
    return res.json(apiResponse)
  } catch (error) {
    next(error)
  }
}

const getAllCustomTemplates = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const apiResponse = await marketplacesService.getAllCustomTemplates()
    return res.json(apiResponse)
  } catch (error) {
    next(error)
  }
}

const saveCustomTemplate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if ((!req.body && !(req.body.chatflowId || req.body.tool)) || !req.body.name) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, `Error: marketplacesService.saveCustomTemplate - body not provided!`)
    }
    const apiResponse = await marketplacesService.saveCustomTemplate(req)
    return res.json(apiResponse)
  } catch (error) {
    next(error)
  }
}

export default {
  getAllTemplates,
  getAllCustomTemplates,
  saveCustomTemplate,
  deleteCustomTemplate
}
