import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm'
import { User } from './User'

@Entity('group_users')
export class GroupUsers {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ type: 'varchar', length: 255, unique: true })
  groupname: string

  @OneToMany(() => User, (user) => user.group)
  users: User[]

  @ManyToOne(() => GroupUsers, (group) => group.childGroup, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'parentGroupId' })
  parentGroup: GroupUsers

  @OneToMany(() => GroupUsers, (group) => group.parentGroup, { nullable: true })
  childGroup: GroupUsers[]

  @Column('varchar', { nullable: true })
  displayPrefixes: string

  @Column({ type: 'timestamp' })
  @CreateDateColumn()
  createdDate: Date

  @Column({ type: 'timestamp' })
  @UpdateDateColumn()
  updatedDate: Date
}
