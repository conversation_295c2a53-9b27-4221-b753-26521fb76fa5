import { StatusCodes } from 'http-status-codes'
import { v4 as uuidv4 } from 'uuid'
import { ChatFlow } from '../../database/entities/ChatFlow'
import { Label } from '../../database/entities/Label'
import { User } from '../../database/entities/User'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { getErrorMessage } from '../../errors/utils'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import {
  addDocuments,
  addUserProvidedEmbedderSetting,
  basicSearch,
  createIndex,
  deleteAllDocuments,
  deleteDocuments,
  deleteIndex,
  getAllDocuments,
  getDocumentById,
  updateDocument,
  vectorSearch
} from '../meilisearch/meilisearch'
import { analyze, ChatHistory, classifyQuestions, generateSystemPromptModel, QAItem } from '../model'
import { Request } from 'express'
import { IChatFlow } from '../../Interface'
import { ChatMessage } from '../../database/entities/ChatMessage'
import { QA } from '../../database/entities/AQ'

const validateUser = async (req: any) => {
  const { user } = req
  if (!user.id) {
    throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Error: documentStoreServices.getAllDocumentStores - User not found')
  }
  const appServer = getRunningExpressApp()
  const foundUser = await appServer.AppDataSource.getRepository(User).findOneBy({ id: user.id })
  if (!foundUser) {
    throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Error: documentStoreServices.getAllDocumentStores - User not found')
  }
}

const saveFaq = async (req: any) => {
  try {
    const { question, answer, chatflowId } = req.body

    if (!question) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: faqsService.saveFaq - question or answer not provided!')
    }

    const document = {
      id: uuidv4(),
      question,
      answer,
      chatflowId,
      createdDate: new Date(),
      updatedDate: new Date()
    }

    await validateUser(req)
    await addDocuments(`document_${chatflowId}`, [document])
    return document
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.saveFaq - ${getErrorMessage(error)}`)
  }
}

const importFaqs = async (req: any) => {
  try {
    const { faqsData, chatflowId } = req.body

    const documents = faqsData.map((faq: any) => ({
      id: uuidv4(),
      question: faq.question,
      answer: faq.answer,
      chatflowId,
      createdDate: new Date(),
      updatedDate: new Date()
    }))
    await validateUser(req)
    await addDocuments(`document_${chatflowId}`, documents)
    return documents
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.importFaqs - ${getErrorMessage(error)}`)
  }
}

const getListOfFlowUsingFAQ = async () => {
  try {
    const appServer = getRunningExpressApp()
    const chatFlowRepo = appServer.AppDataSource.getRepository(ChatFlow)
    const flowsUsingFAQ = await chatFlowRepo
      .createQueryBuilder('chatFlow')
      .select('chatFlow.id')
      .where('chatFlow.isUseFAQ = :isUseFAQ OR chatFlow.showDashboard = :showDashboard', {
        isUseFAQ: true,
        showDashboard: true
      })
      .getRawMany()

    return flowsUsingFAQ
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: faqsService.getListOfFlowUsingFAQ - ${getErrorMessage(error)}`
    )
  }
}

const getAllFaqs = async (req: any) => {
  const chatflowId = req.query?.chatflowId
  let { limit, offset } = req?.query || {}

  limit = parseInt(limit, 10) || 20
  offset = parseInt(offset, 10) || 0

  try {
    await validateUser(req)
    return await getAllDocuments(`document_${chatflowId}`, limit, offset)
  } catch (error: any) {
    if (error.code === 'index_not_found' && error.httpStatus === 404) {
      await createIndex(`document_${chatflowId}`)
      return await getAllDocuments(`document_${chatflowId}`, limit, offset)
    }
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `${getErrorMessage(error)}`)
  }
}

const getFaqById = async (id: string, chatflowId: string) => {
  try {
    return await getDocumentById(`document_${chatflowId}`, id)
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.getFaqById - ${getErrorMessage(error)}`)
  }
}

const updateFaq = async (req: any) => {
  try {
    const { question, answer, chatflowId } = req.body

    const { id } = req.params

    if (!chatflowId || !id) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: faqsService.updateFaq - chatflowId or id not provided!')
    }

    if (!question || !answer) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: faqsService.updateFaq - question or answer not provided!')
    }

    const newDocument = {
      id: id,
      question,
      answer,
      chatflowId,
      updatedDate: new Date()
    }

    await validateUser(req)
    await updateDocument(`document_${chatflowId}`, newDocument)
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.updateFaq - ${getErrorMessage(error)}`)
  }
}

const deleteFaq = async (req: any) => {
  try {
    const { chatflowId, id } = req.params

    if (!chatflowId || !id) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: faqsService.deleteFaq - chatflowId or id not provided!')
    }

    await validateUser(req)
    await deleteDocuments(`document_${chatflowId}`, [id])
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.deleteFaq - ${getErrorMessage(error)}`)
  }
}

const deleteAllFaqs = async (req: any) => {
  try {
    const chatflowId = req.params?.chatflowId
    await validateUser(req)
    await deleteAllDocuments(`document_${chatflowId}`)
    await deleteIndex(`document_${chatflowId}`)

    return { message: 'All FAQs deleted successfully' }
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.deleteAllFaqs - ${getErrorMessage(error)}`)
  }
}

const searchFaqs = async (query: string, searchParams: any, req: any) => {
  try {
    const chatflowId = req.params?.chatflowId
    return await basicSearch(`document_${chatflowId}`, query, searchParams)
    // return await vectorSearch(`document_${chatflowId}`, query, searchParams)
    // return await hybridSearch(`document_${chatflowId}`, query, searchParams)
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.searchFaqs - ${getErrorMessage(error)}`)
  }
}

const vectorSearchFaqs = async (query: string, searchParams: any, req: any) => {
  try {
    const chatflowId = req.params?.chatflowId
    return await vectorSearch(`document_${chatflowId}`, query, searchParams)
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.vectorSearchFaqs - ${getErrorMessage(error)}`)
  }
}

const deleteIndexService = async (chatflowId: string) => {
  try {
    await deleteIndex(`document_${chatflowId}`)
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.deleteIndex - ${getErrorMessage(error)}`)
  }
}

const updateSettingsService = async (req: any) => {
  try {
    const { chatflowId } = req.params

    if (!chatflowId) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'chatflowId not provided!')
    }

    await validateUser(req)
    return await addUserProvidedEmbedderSetting(`document_${chatflowId}`)
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `${getErrorMessage(error)}`)
  }
}

const cronJobRefineFaqFromHistory = async (req: any) => {
  try {
    let { chatflowId, minute } = req.body
    return
  } catch (error) {
    console.log('🚀 ~ index.ts:365 ~ cronJobRefineFaqFromHistory ~ error:', error)
  }
}

const fetchClassifyQARecords = async (req: any) => {
  try {
    let { chatflowId, minute } = req.body
    return
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: fetchRecentQARecords - ${getErrorMessage(error)}`)
  }
}

const getRecentQARecords = async (req: any) => {
  try {
    const { chatflowId } = req.params

    if (!chatflowId) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: getRecentQARecords - chatflowId not provided!')
    }

    const appServer = getRunningExpressApp()
    const recentRecords = await appServer.AppDataSource.getRepository(Label)
      .createQueryBuilder('label')
      .where('label.chatflowid = :chatflowId', { chatflowId })
      .orderBy('label.createdDate', 'DESC')
      .getMany()

    const totalRecords = recentRecords.reduce((sum, record) => sum + Number(record.count), 0)

    const responseData = recentRecords.map((record) => ({
      category: record.label,
      total: Number(record.count),
      percent: parseFloat(((record.count / totalRecords) * 100).toFixed(1)),
      chatflowid: record.chatflowid,
      id: record.id
    }))

    return { data: responseData }
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: getRecentQARecords - ${getErrorMessage(error)}`)
  }
}

const addOrUpdateLabel = async (req: any) => {
  try {
    const { chatflowId, id, label } = req.body

    if (!chatflowId || !label) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: addOrUpdateLabel - chatflowId or label not provided!')
    }

    const appServer = getRunningExpressApp()
    const labelRepo = appServer.AppDataSource.getRepository(Label)

    let existingLabel = null
    if (id) {
      existingLabel = await labelRepo.findOne({
        where: { id, chatflowid: chatflowId }
      })

      if (!existingLabel) {
        throw new InternalFlowiseError(StatusCodes.NOT_FOUND, 'Error: addOrUpdateLabel - Label not found!')
      }
    }

    if (existingLabel) {
      existingLabel.label = label
      await labelRepo.save(existingLabel)
      return { message: 'Label updated successfully', label: existingLabel }
    } else {
      const newLabel = labelRepo.create({
        id: id || uuidv4(),
        label,
        count: 0,
        chatflowid: chatflowId
      })
      await labelRepo.save(newLabel)
      return { message: 'Label added successfully', label: newLabel }
    }
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: addOrUpdateLabel - ${getErrorMessage(error)}`)
  }
}

const removeLabel = async (req: any) => {
  try {
    const { chatflowId, id } = req.params

    if (!chatflowId || !id) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: removeLabel - chatflowId or id not provided!')
    }

    const appServer = getRunningExpressApp()
    const labelRepo = appServer.AppDataSource.getRepository(Label)

    const existingLabel = await labelRepo.findOne({
      where: { id, chatflowid: chatflowId }
    })

    if (!existingLabel) {
      throw new InternalFlowiseError(StatusCodes.NOT_FOUND, 'Error: removeLabel - Label not found!')
    }

    await labelRepo.delete({ id, chatflowid: chatflowId })
    return { message: 'Label removed successfully' }
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: removeLabel - ${getErrorMessage(error)}`)
  }
}

const generateSystemPrompt = async (req: Request) => {
  try {
    const { promptDescription } = req.body
    const result = await generateSystemPromptModel(promptDescription)
    return { text: result }
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Prompt generation failed: ${getErrorMessage(error)}`)
  }
}

export const refineMessageToQA = async (chatflow: IChatFlow, chatId: string, user: User) => {
  try {
    const appServer = getRunningExpressApp()
    const chatMessageRepo = appServer.AppDataSource.getRepository(ChatMessage)
    const QARepo = appServer.AppDataSource.getRepository(QA)

    const chatMessage = await chatMessageRepo.find({
      where: { chatId, chatflowid: chatflow.id, scanEd: false },
      order: { createdDate: 'ASC' }
    })

    const listMsg: ChatHistory[] = []
    // const listAQ: QAItem[] = []

    if (chatMessage.length > 1) {
      for (let i = 0; i < chatMessage.length; i++) {
        const msg = chatMessage[i]
        const { chatId, role, content, createdDate } = msg

        if (role === 'userMessage') {
          const nextMsg = chatMessage[i + 1]
          if (nextMsg && nextMsg.chatId === chatId && nextMsg.role === 'apiMessage') {
            listMsg.push({
              id: uuidv4(),
              question: content,
              answer: nextMsg.content,
              time: createdDate
            })
            i++
          }
        }
      }

      // const qaList_chatId = await QARepo.find({
      //   where: { chatId, chatflowid: chatflow.id },
      //   order: { createdDate: 'DESC' },
      //   take: 3
      // })

      // if (qaList_chatId.length > 0) {
      //   for (let i = 0; i < qaList_chatId.length; i++) {
      //     const msg = qaList_chatId[i]
      //     const { question, answer } = msg
      //     listAQ.push({
      //       question,
      //       answer,
      //       time: msg.createdDate
      //     })
      //   }
      // }

      if (listMsg.length > 1) {
        // const stringResponse = await analyze(listMsg, listAQ)
        // console.log('🚀 ~ index.ts:414 ~ refineMessageToQA ~ stringResponse:', stringResponse)
        // let arrayResponse: {
        //   id: string
        //   question: string
        //   answer: string
        //   time: Date
        // }[] = []
        // try {
        //   arrayResponse = JSON.parse(stringResponse)
        // } catch (error) {
        //   console.log('🚀 ~ index.ts:251 ~ error:', error)
        // }

        const newAqsContent = listMsg.map((item) =>
          QARepo.create({
            id: item.id === '1' ? uuidv4() : item.id,
            answer: item?.answer || '',
            question: item?.question || '',
            question_type: ''
          })
        )

        if (newAqsContent?.length > 0) {
          for (const content of newAqsContent) {
            const existingRecord = await QARepo.findOneBy({ id: content.id })
            if (existingRecord) {
              await QARepo.update({ id: content.id }, content)
            } else {
              await QARepo.save({
                ...content,
                timeLastCron: new Date(),
                userId: user?.id,
                userName: user?.username || '',
                chatId: chatId,
                chatflowid: chatflow.id,
                scanEd: false
              })
            }
          }
        }

        if (chatMessage.length > 0) {
          for (const msg of chatMessage) {
            await chatMessageRepo.update({ id: msg.id }, { scanEd: true })
          }
        }
      }
    }

    const qaList = await QARepo.find({
      where: { scanEd: false, chatflowid: chatflow.id },
      order: { createdDate: 'DESC' }
    })

    if (qaList.length > 3) {
      const labelRecords = await appServer.AppDataSource.getRepository(Label).find({
        where: { chatflowid: chatflow.id },
        order: {
          createdDate: 'DESC'
        }
      })

      const list_label = labelRecords.map((label) => label.label)
      const list_qa = qaList.map((qa) => ({
        question: qa.question,
        answer: qa.answer
      }))

      const textLabel = await classifyQuestions(list_qa, list_label)
      let arrayResponse: { question: string; label: string }[] = []

      try {
        arrayResponse = JSON.parse(textLabel)
      } catch (error) {
        console.log('🚀 ~ classifyAndUpdateLabels ~ error:', error)
      }
      console.log('🚀 ~ index.ts:487 ~ refineMessageToQA ~ arrayResponse:', arrayResponse)

      if (arrayResponse?.length > 0) {
        const labelRepo = appServer.AppDataSource.getRepository(Label)
        const labelMap: { [key: string]: number } = {}

        arrayResponse.forEach((item) => {
          if (labelMap[item.label]) {
            labelMap[item.label] += 1
          } else {
            labelMap[item.label] = 1
          }
        })

        const labelUpdates = Object.entries(labelMap).map(async ([label, count]) => {
          const existingLabel = await labelRepo.findOne({
            where: { label, chatflowid: chatflow.id }
          })

          if (existingLabel) {
            existingLabel.count = Number(existingLabel.count) + Number(count)
            return labelRepo.save(existingLabel)
          } else {
            return labelRepo.save(
              labelRepo.create({
                label,
                count: Number(count),
                chatflowid: chatflow.id
              })
            )
          }
        })

        await Promise.all(labelUpdates)

        if (qaList.length > 0) {
          for (const qa of qaList) {
            await QARepo.update({ id: qa.id }, { scanEd: true })
          }
        }
      }
    }

    return null
  } catch (error) {
    console.log('🚀 ~ index.ts:541 ~ refineMessageToQA ~ error:', error)
  }
}

export const faqsService = {
  saveFaq,
  importFaqs,
  getAllFaqs,
  getFaqById,
  updateFaq,
  deleteFaq,
  deleteAllFaqs,
  searchFaqs,
  deleteIndexService,
  updateSettingsService,
  vectorSearchFaqs,
  cronJobRefineFaqFromHistory,
  getListOfFlowUsingFAQ,
  fetchClassifyQARecords,
  getRecentQARecords,
  addOrUpdateLabel,
  removeLabel,
  generateSystemPrompt,
  refineMessageToQA
}
