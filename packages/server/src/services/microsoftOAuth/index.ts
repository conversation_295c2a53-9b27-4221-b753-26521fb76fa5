import axios from 'axios'
import { StatusCodes } from 'http-status-codes'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import { User, UserRole } from '../../database/entities/User'
import { GroupUsers } from '../../database/entities/GroupUser'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { v4 as uuidv4 } from 'uuid'

interface MicrosoftUserInfo {
  id: string
  displayName: string
  mail: string
  userPrincipalName: string
  givenName?: string
  surname?: string
}

interface MicrosoftOAuthConfig {
  clientId: string
  clientSecret: string
  tenantId: string
}

class MicrosoftOAuthService {
  private config: MicrosoftOAuthConfig

  constructor() {
    this.config = {
      clientId: process.env.MICROSOFT_CLIENT_ID || '',
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET || '',
      tenantId: process.env.MICROSOFT_TENANT_ID || 'common'
    }

    if (!this.config.clientId || !this.config.clientSecret) {
      console.warn(
        '🔐 [Microsoft OAuth]: Microsoft OAuth configuration is incomplete. Please set MICROSOFT_CLIENT_ID and MICROSOFT_CLIENT_SECRET environment variables.'
      )
    }
  }

  /**
   * Validate Microsoft access token with Microsoft Graph API
   */
  async validateAccessToken(accessToken: string): Promise<MicrosoftUserInfo> {
    try {
      console.log('🔐 [Microsoft OAuth]: Validating access token with Microsoft Graph API')

      const response = await axios.get('https://graph.microsoft.com/v1.0/me', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      })

      console.log('🔐 [Microsoft OAuth]: Successfully validated access token', {
        userId: response.data.id,
        email: response.data.mail || response.data.userPrincipalName,
        displayName: response.data.displayName
      })

      return response.data
    } catch (error: any) {
      console.error('🔐 [Microsoft OAuth]: Failed to validate access token', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText
      })

      if (error.response?.status === 401) {
        throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Invalid or expired Microsoft access token')
      } else if (error.response?.status === 403) {
        throw new InternalFlowiseError(StatusCodes.FORBIDDEN, 'Insufficient permissions to access Microsoft Graph API')
      } else {
        throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Failed to validate Microsoft access token')
      }
    }
  }

  /**
   * Find or create Microsoft Users group
   */
  async findOrCreateMicrosoftGroup(): Promise<string> {
    const appServer = getRunningExpressApp()
    const groupRepository = appServer.AppDataSource.getRepository(GroupUsers)

    const groupName = 'Microsoft_Users'

    // Try to find existing group
    let group = await groupRepository.findOne({
      where: { groupname: groupName }
    })

    if (!group) {
      // Create the group if it doesn't exist
      console.log('🔐 [Microsoft OAuth]: Creating Microsoft_Users group')
      const newGroup = new GroupUsers()
      newGroup.id = uuidv4()
      newGroup.groupname = groupName
      newGroup.displayPrefixes = 'Microsoft OAuth Users'

      group = await groupRepository.save(newGroup)
      console.log('🔐 [Microsoft OAuth]: Successfully created Microsoft_Users group', {
        groupId: group.id,
        groupname: group.groupname
      })
    }

    return group.groupname
  }

  /**
   * Find or create user based on Microsoft user info
   * Uses existing User entity fields without adding new ones
   */
  async findOrCreateUser(microsoftUserInfo: MicrosoftUserInfo): Promise<User> {
    const appServer = getRunningExpressApp()
    const userRepository = appServer.AppDataSource.getRepository(User)

    // Use email from Microsoft (mail or userPrincipalName)
    const email = microsoftUserInfo.mail || microsoftUserInfo.userPrincipalName
    if (!email) {
      throw new InternalFlowiseError(StatusCodes.BAD_REQUEST, 'Microsoft account does not have a valid email address')
    }

    console.log('🔐 [Microsoft OAuth]: Looking for existing user', { email })

    // Try to find existing user by email
    let user = await userRepository.findOne({
      where: { email: email.toLowerCase() }
    })

    if (user) {
      console.log('🔐 [Microsoft OAuth]: Found existing user', {
        userId: user.id,
        username: user.username,
        email: user.email
      })

      // Update user's display name using displayPrefixes field (existing field)
      if (microsoftUserInfo.displayName && user.displayPrefixes !== microsoftUserInfo.displayName) {
        user.displayPrefixes = microsoftUserInfo.displayName
        await userRepository.save(user)
        console.log('🔐 [Microsoft OAuth]: Updated user display name', {
          userId: user.id,
          displayName: microsoftUserInfo.displayName
        })
      }

      return user
    }

    // Create new user using existing fields only
    console.log('🔐 [Microsoft OAuth]: Creating new user from Microsoft account', {
      email: email,
      displayName: microsoftUserInfo.displayName
    })

    // Ensure Microsoft_Users group exists
    const groupName = await this.findOrCreateMicrosoftGroup()

    const newUser = new User()
    newUser.id = uuidv4()
    newUser.username = email // Use email as username
    newUser.email = email.toLowerCase()
    newUser.password = await bcrypt.hash(uuidv4(), 10) // Random password for OAuth users
    newUser.role = UserRole.USER
    newUser.active = true
    newUser.groupname = groupName // Use the group that we ensured exists
    newUser.displayPrefixes = microsoftUserInfo.displayName || '' // Store display name in existing field

    const savedUser = await userRepository.save(newUser)
    console.log('🔐 [Microsoft OAuth]: Successfully created new user', {
      userId: savedUser.id,
      username: savedUser.username,
      email: savedUser.email,
      displayName: savedUser.displayPrefixes,
      groupname: savedUser.groupname
    })

    return savedUser
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCode(code: string, redirectUri: string): Promise<{ accessToken: string; userInfo: MicrosoftUserInfo }> {
    try {
      console.log('🔐 [Microsoft OAuth]: Starting authorization code exchange', {
        codeLength: code.length,
        codePreview: code.substring(0, 20) + '...',
        redirectUri,
        clientId: this.config.clientId ? 'present' : 'missing',
        clientSecret: this.config.clientSecret ? 'present' : 'missing',
        tenantId: this.config.tenantId
      })

      if (!this.config.clientId || !this.config.clientSecret) {
        console.error('🔐 [Microsoft OAuth]: Missing configuration', {
          hasClientId: !!this.config.clientId,
          hasClientSecret: !!this.config.clientSecret,
          tenantId: this.config.tenantId
        })
        throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Microsoft OAuth configuration is incomplete')
      }

      const tokenUrl = `https://login.microsoftonline.com/${this.config.tenantId}/oauth2/v2.0/token`
      const tokenData = new URLSearchParams({
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        code: code,
        redirect_uri: redirectUri,
        grant_type: 'authorization_code'
      })

      console.log('🔐 [Microsoft OAuth]: Making token exchange request', {
        url: tokenUrl,
        dataLength: tokenData.toString().length,
        hasCode: tokenData.has('code'),
        hasRedirectUri: tokenData.has('redirect_uri')
      })

      // Exchange authorization code for access token
      const tokenResponse = await axios.post(tokenUrl, tokenData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        timeout: 10000
      })

      console.log('🔐 [Microsoft OAuth]: Successfully exchanged code for access token', {
        hasAccessToken: !!tokenResponse.data.access_token,
        tokenLength: tokenResponse.data.access_token?.length,
        expiresIn: tokenResponse.data.expires_in,
        tokenType: tokenResponse.data.token_type,
        responseKeys: Object.keys(tokenResponse.data)
      })

      const accessToken = tokenResponse.data.access_token

      if (!accessToken) {
        console.error('🔐 [Microsoft OAuth]: No access token in response', {
          responseData: tokenResponse.data
        })
        throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'No access token received from Microsoft')
      }

      // Get user info using the access token
      const userInfo = await this.validateAccessToken(accessToken)

      return {
        accessToken,
        userInfo
      }
    } catch (error: any) {
      console.error('🔐 [Microsoft OAuth]: Failed to exchange authorization code', {
        error: error.message,
        errorType: error.constructor.name,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers
        }
      })

      if (error.response?.status === 400) {
        throw new InternalFlowiseError(
          StatusCodes.BAD_REQUEST,
          `Invalid authorization code or redirect URI: ${error.response.data?.error_description || error.response.data?.error}`
        )
      } else if (error.response?.status === 401) {
        throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Invalid client credentials')
      } else {
        throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Failed to exchange authorization code: ${error.message}`)
      }
    }
  }

  /**
   * Main login method for Microsoft OAuth
   */
  async loginWithMicrosoft(accessToken: string): Promise<{ user: User; accessToken: string; refreshToken: string }> {
    try {
      console.log('🔐 [Microsoft OAuth]: Starting Microsoft OAuth login process')

      // Validate the access token with Microsoft Graph API
      const microsoftUserInfo = await this.validateAccessToken(accessToken)

      // Find or create user in our database
      const user = await this.findOrCreateUser(microsoftUserInfo)

      // Check if user is active
      if (!user.active) {
        throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Account has been deactivated')
      }

      // Generate JWT tokens
      if (!process.env.ACCESS_TOKEN_SECRET || !process.env.REFRESH_TOKEN_SECRET) {
        throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, 'Token secrets are not configured')
      }

      const jwtAccessToken = jwt.sign(
        {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          loginMethod: 'microsoft'
        },
        process.env.ACCESS_TOKEN_SECRET,
        { expiresIn: '1d' }
      )

      const refreshToken = jwt.sign(
        {
          id: user.id,
          username: user.username,
          email: user.email,
          loginMethod: 'microsoft'
        },
        process.env.REFRESH_TOKEN_SECRET,
        { expiresIn: '7d' }
      )

      console.log('🔐 [Microsoft OAuth]: Microsoft OAuth login completed successfully', {
        userId: user.id,
        username: user.username,
        email: user.email,
        displayName: user.displayPrefixes
      })

      return {
        user,
        accessToken: jwtAccessToken,
        refreshToken
      }
    } catch (error: any) {
      console.error('🔐 [Microsoft OAuth]: Microsoft OAuth login failed', {
        error: error.message,
        statusCode: error.statusCode || StatusCodes.INTERNAL_SERVER_ERROR
      })
      throw error
    }
  }
}

export default new MicrosoftOAuthService()
