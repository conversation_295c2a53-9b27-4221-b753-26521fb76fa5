import { DeleteResult, FindOptionsWhere } from 'typeorm'
import { StatusCodes } from 'http-status-codes'
import { ChatMessageRatingType, ChatType, IChatMessage } from '../../Interface'
import { utilGetChatMessage } from '../../utils/getChatMessage'
import { utilAddChatMessage } from '../../utils/addChatMesage'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import { ChatMessageFeedback } from '../../database/entities/ChatMessageFeedback'
import { removeFilesFromStorage } from 'flowise-components'
import logger from '../../utils/logger'
import { ChatMessage } from '../../database/entities/ChatMessage'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { getErrorMessage } from '../../errors/utils'

// Add chatmessages for chatflowid
const createChatMessage = async (chatMessage: Partial<IChatMessage>) => {
  try {
    const dbResponse = await utilAddChatMessage(chatMessage)
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatMessagesService.createChatMessage - ${getErrorMessage(error)}`
    )
  }
}

// Get all chatmessages from chatflowid
const getAllChatMessages = async (
  chatflowId: string,
  chatTypeFilter: ChatType | undefined,
  sortOrder: string = 'ASC',
  chatId?: string,
  memoryType?: string,
  sessionId?: string,
  startDate?: string,
  endDate?: string,
  messageId?: string,
  feedback?: boolean,
  feedbackTypes?: ChatMessageRatingType[]
): Promise<ChatMessage[]> => {
  try {
    const dbResponse = await utilGetChatMessage(
      chatflowId,
      chatTypeFilter,
      sortOrder,
      chatId,
      memoryType,
      sessionId,
      startDate,
      endDate,
      messageId,
      feedback,
      feedbackTypes
    )
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatMessagesService.getAllChatMessages - ${getErrorMessage(error)}`
    )
  }
}

// Get internal chatmessages from chatflowid
const getAllInternalChatMessages = async (
  chatflowId: string,
  chatTypeFilter: ChatType | undefined,
  sortOrder: string = 'ASC',
  chatId?: string,
  memoryType?: string,
  sessionId?: string,
  startDate?: string,
  endDate?: string,
  messageId?: string,
  feedback?: boolean,
  feedbackTypes?: ChatMessageRatingType[]
): Promise<ChatMessage[]> => {
  try {
    const dbResponse = await utilGetChatMessage(
      chatflowId,
      chatTypeFilter,
      sortOrder,
      chatId,
      memoryType,
      sessionId,
      startDate,
      endDate,
      messageId,
      feedback,
      feedbackTypes
    )
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatMessagesService.getAllInternalChatMessages - ${getErrorMessage(error)}`
    )
  }
}

// Get all messages from a specific chatflowId, grouped by chatId
const getAllMessagesByChatflowId = async (chatflowId: string) => {
  try {
    const appServer = getRunningExpressApp()
    const messages = await appServer.AppDataSource.getRepository(ChatMessage)
      .createQueryBuilder('chat')
      .where('chat.chatflowid = :chatflowid', { chatflowid: chatflowId })
      .orderBy('chat.createdDate', 'DESC')
      .getMany()

    return messages
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatMessagesService.getAllMessagesByChatflowId - ${getErrorMessage(error)}`
    )
  }
}

const removeAllChatMessages = async (
  chatId: string,
  chatflowid: string,
  deleteOptions: FindOptionsWhere<ChatMessage>
): Promise<DeleteResult> => {
  try {
    const appServer = getRunningExpressApp()

    // Remove all related feedback records
    const feedbackDeleteOptions: FindOptionsWhere<ChatMessageFeedback> = { chatId }
    await appServer.AppDataSource.getRepository(ChatMessageFeedback).delete(feedbackDeleteOptions)

    // Delete all uploads corresponding to this chatflow/chatId
    if (chatId) {
      try {
        await removeFilesFromStorage(chatflowid, chatId)
      } catch (e) {
        logger.error(`[server]: Error deleting file storage for chatflow ${chatflowid}, chatId ${chatId}: ${e}`)
      }
    }
    const dbResponse = await appServer.AppDataSource.getRepository(ChatMessage).delete(deleteOptions)
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatMessagesService.removeAllChatMessages - ${getErrorMessage(error)}`
    )
  }
}

const removeChatMessagesByMessageIds = async (
  chatflowid: string,
  chatIdMap: Map<string, ChatMessage[]>,
  messageIds: string[]
): Promise<DeleteResult> => {
  try {
    const appServer = getRunningExpressApp()

    for (const [composite_key] of chatIdMap) {
      const [chatId] = composite_key.split('_')

      // Remove all related feedback records
      const feedbackDeleteOptions: FindOptionsWhere<ChatMessageFeedback> = { chatId }
      await appServer.AppDataSource.getRepository(ChatMessageFeedback).delete(feedbackDeleteOptions)

      // Delete all uploads corresponding to this chatflow/chatId
      await removeFilesFromStorage(chatflowid, chatId)
    }

    const dbResponse = await appServer.AppDataSource.getRepository(ChatMessage).delete(messageIds)
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatMessagesService.removeAllChatMessages - ${getErrorMessage(error)}`
    )
  }
}

const abortChatMessage = async (chatId: string, chatflowid: string) => {
  try {
    const appServer = getRunningExpressApp()

    const endingNodeData = appServer.chatflowPool.activeChatflows[`${chatflowid}_${chatId}`]?.endingNodeData as any

    if (endingNodeData && endingNodeData.signal) {
      try {
        endingNodeData.signal.abort()
        await appServer.chatflowPool.remove(`${chatflowid}_${chatId}`)
      } catch (e) {
        logger.error(`[server]: Error aborting chat message for ${chatflowid}, chatId ${chatId}: ${e}`)
      }
    }
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatMessagesService.abortChatMessage - ${getErrorMessage(error)}`
    )
  }
}

export default {
  createChatMessage,
  getAllChatMessages,
  getAllInternalChatMessages,
  removeAllChatMessages,
  removeChatMessagesByMessageIds,
  abortChatMessage,
  getAllMessagesByChatflowId
}
